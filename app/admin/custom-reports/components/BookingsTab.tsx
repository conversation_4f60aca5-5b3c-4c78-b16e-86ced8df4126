'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Search, Download, Filter, Crown } from 'lucide-react';
import { toast } from 'sonner';

interface DateRange {
  startDate: string;
  endDate: string;
}

interface Booking {
  id: number;
  booking_id?: string;
  admission_number: string;
  student_name: string;
  routeName: string;
  destination: string;
  payment_status: boolean;
  fare: number;
  is_special: boolean | null;
  created_at: string;
  updated_at: string;
  boarded: boolean;
}

interface BookingsData {
  bookings: Booking[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary: {
    totalBookings: number;
    paidBookings: number;
    unpaidBookings: number;
    totalRevenue: number;
    uniqueRoutes: number;
  };
  availableRoutes: string[];
}

interface BookingsTabProps {
  dateRange: DateRange;
  isRefreshing: boolean;
}

export default function BookingsTab({ dateRange, isRefreshing }: BookingsTabProps) {
  const [data, setData] = useState<BookingsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRoute, setSelectedRoute] = useState<string>('all');
  const [paymentFilter, setPaymentFilter] = useState<string>('all');
  const [pageSize] = useState(25);

  const fetchBookingsData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        start_date: dateRange.startDate,
        end_date: dateRange.endDate,
        page: currentPage.toString(),
        limit: pageSize.toString()
      });

      if (selectedRoute !== 'all') {
        params.append('bus_route', selectedRoute);
      }

      if (paymentFilter !== 'all') {
        params.append('payment_status', paymentFilter);
      }

      const response = await fetch(`/api/admin/custom-reports/bookings?${params}`, {
        credentials: 'include'
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch bookings data');
        toast.error('Failed to fetch bookings data');
      }
    } catch (error) {
      setError('Network error while fetching bookings data');
      toast.error('Network error while fetching bookings data');
      console.error('Bookings fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  }, [dateRange, currentPage, pageSize, selectedRoute, paymentFilter]);

  // Fetch data when dependencies change
  useEffect(() => {
    fetchBookingsData();
  }, [fetchBookingsData]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [selectedRoute, paymentFilter, searchTerm]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleExport = () => {
    if (!data) return;
    
    // Create CSV content
    const headers = ['ID', 'Booking ID', 'Special Booking', 'Admission Number', 'Student Name', 'Route Name', 'Destination', 'Payment Status', 'Fare', 'Created At'];
    const csvContent = [
      headers.join(','),
      ...data.bookings.map(booking => [
        booking.id,
        booking.booking_id || 'N/A',
        booking.is_special ? 'Yes' : 'No',
        booking.admission_number,
        `"${booking.student_name}"`,
        booking.routeName,
        `"${booking.destination}"`,
        booking.payment_status ? 'Paid' : 'Unpaid',
        booking.fare || 0,
        new Date(booking.created_at).toLocaleString()
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bookings-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    
    toast.success('Bookings data exported successfully');
  };

  // Filter bookings by search term (client-side)
  const filteredBookings = data?.bookings.filter(booking => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();
    return (
      booking.admission_number.toLowerCase().includes(term) ||
      booking.student_name.toLowerCase().includes(term) ||
      booking.routeName.toLowerCase().includes(term) ||
      booking.destination.toLowerCase().includes(term)
    );
  }) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading bookings data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={fetchBookingsData} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">{data.summary.totalBookings}</div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Total Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-xl sm:text-2xl font-bold text-green-600">{data.summary.paidBookings}</div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Paid Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-xl sm:text-2xl font-bold text-orange-600">{data.summary.unpaidBookings}</div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Unpaid Bookings</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-xl sm:text-2xl font-bold text-purple-600">₹{data.summary.totalRevenue}</div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Total Revenue</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 sm:p-6 text-center">
            <div className="text-xl sm:text-2xl font-bold text-indigo-600">{data.summary.uniqueRoutes}</div>
            <div className="text-xs sm:text-sm text-gray-600 mt-1">Unique Routes</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <span className="flex items-center gap-2">
              <Filter className="w-5 h-5" />
              <span className="text-lg sm:text-xl">Filters & Search</span>
            </span>
            <Button onClick={handleExport} variant="outline" size="sm" className="h-10 self-start sm:self-auto">
              <Download className="w-4 h-4 mr-2" />
              Export CSV
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="sm:col-span-2 lg:col-span-1">
              <Input
                placeholder="Search by name, admission number, route name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full h-10 text-base"
              />
            </div>
            <div>
              <Select value={selectedRoute} onValueChange={setSelectedRoute}>
                <SelectTrigger className="h-10 text-base">
                  <SelectValue placeholder="Filter by route name" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Routes</SelectItem>
                  {data.availableRoutes.map(route => (
                    <SelectItem key={route} value={route}>{route}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                <SelectTrigger className="h-10 text-base">
                  <SelectValue placeholder="Filter by payment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Payments</SelectItem>
                  <SelectItem value="true">Paid</SelectItem>
                  <SelectItem value="false">Unpaid</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedRoute('all');
                  setPaymentFilter('all');
                }}
                variant="outline"
                className="w-full h-10 text-base"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg sm:text-xl">Bookings List</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[80px]">ID</TableHead>
                    <TableHead className="min-w-[120px]">Booking ID</TableHead>
                    <TableHead className="min-w-[140px]">Admission Number</TableHead>
                    <TableHead className="min-w-[180px]">Student Name</TableHead>
                    <TableHead className="min-w-[140px]">Route Name</TableHead>
                    <TableHead className="min-w-[160px]">Destination</TableHead>
                    <TableHead className="min-w-[120px]">Payment Status</TableHead>
                    <TableHead className="min-w-[80px]">Fare</TableHead>
                    <TableHead className="min-w-[120px]">Created At</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBookings.length > 0 ? (
                    filteredBookings.map((booking) => (
                      <TableRow key={booking.id}>
                        <TableCell className="flex items-center gap-2">
                          {booking.id}
                          {booking.is_special && (
                            <Crown className="w-4 h-4 text-yellow-500">
                              <title>Admin Booked</title>
                            </Crown>
                          )}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {booking.booking_id || 'N/A'}
                        </TableCell>
                        <TableCell className="font-medium">{booking.admission_number}</TableCell>
                        <TableCell>{booking.student_name}</TableCell>
                        <TableCell>{booking.routeName}</TableCell>
                        <TableCell>{booking.destination}</TableCell>
                        <TableCell>
                          <Badge variant={booking.payment_status ? "default" : "secondary"}>
                            {booking.payment_status ? 'Paid' : 'Unpaid'}
                          </Badge>
                        </TableCell>
                        <TableCell>₹{booking.fare || 0}</TableCell>
                        <TableCell>{new Date(booking.created_at).toLocaleDateString()}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                        No bookings found for the selected criteria
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {filteredBookings.length > 0 ? (
              filteredBookings.map((booking) => (
                <Card key={booking.id} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-500">ID:</span>
                        <span className="font-semibold">{booking.id}</span>
                        {booking.is_special && (
                          <Crown className="w-4 h-4 text-yellow-500" title="Admin Booked" />
                        )}
                      </div>
                      <Badge variant={booking.payment_status ? "default" : "secondary"}>
                        {booking.payment_status ? 'Paid' : 'Unpaid'}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-gray-500">Booking ID:</span>
                        <span className="ml-2 font-mono">{booking.booking_id || 'N/A'}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Admission:</span>
                        <span className="ml-2 font-medium">{booking.admission_number}</span>
                      </div>
                      <div className="sm:col-span-2">
                        <span className="text-gray-500">Student:</span>
                        <span className="ml-2 font-medium">{booking.student_name}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Route:</span>
                        <span className="ml-2">{booking.routeName}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Fare:</span>
                        <span className="ml-2 font-semibold">₹{booking.fare || 0}</span>
                      </div>
                      <div className="sm:col-span-2">
                        <span className="text-gray-500">Destination:</span>
                        <span className="ml-2">{booking.destination}</span>
                      </div>
                      <div className="sm:col-span-2">
                        <span className="text-gray-500">Created:</span>
                        <span className="ml-2">{new Date(booking.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                No bookings found for the selected criteria
              </div>
            )}
          </div>

          {/* Pagination */}
          {data.pagination.totalPages > 1 && (
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-6 pt-4 border-t">
              <div className="text-xs sm:text-sm text-gray-600 text-center sm:text-left">
                Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, data.pagination.total)} of {data.pagination.total} results
              </div>
              <div className="flex items-center justify-center gap-2">
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  variant="outline"
                  size="sm"
                  className="h-10 px-3 sm:px-4"
                >
                  <ChevronLeft className="w-4 h-4" />
                  <span className="hidden sm:inline ml-1">Previous</span>
                </Button>
                <span className="text-xs sm:text-sm px-2 py-1 bg-gray-100 rounded">
                  {currentPage} / {data.pagination.totalPages}
                </span>
                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === data.pagination.totalPages}
                  variant="outline"
                  size="sm"
                  className="h-10 px-3 sm:px-4"
                >
                  <span className="hidden sm:inline mr-1">Next</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
