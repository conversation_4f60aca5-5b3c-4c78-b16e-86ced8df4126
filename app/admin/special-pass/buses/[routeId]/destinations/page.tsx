'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { MapPin, ArrowLeft, CheckCircle, DollarSign, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface RouteStop {
  id: number;
  stop_name: string;
  fare: number;
  stop_order: number;
}

interface Bus {
  name: string;
  route_code: string;
  available_seats: number | null;
}

interface Params {
  routeId: string;
}

function SpecialPassDestinations({ params }: { params: Params }) {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData } = useSpecialPass();
  const [bus, setBus] = useState<Bus | null>(null);
  const [stops, setStops] = useState<RouteStop[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const routeCode = decodeURIComponent(params.routeId.replace('route-', ''));

  // Check if we have required data from previous steps
  useEffect(() => {
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      toast.error('Please select travel dates first');
      router.push('/admin/special-pass');
      return;
    }

    if (!specialPassData.studentName || !specialPassData.admissionNumber) {
      toast.error('Please enter student details first');
      router.push('/admin/special-pass/details');
      return;
    }
  }, [specialPassData, router]);

  const fetchData = useCallback(async () => {
    try {
      const { data: busData } = await supabase
        .from('buses')
        .select('name, route_code, available_seats')
        .eq('route_code', routeCode)
        .eq('is_active', true)
        .single();

      const { data: stopsData } = await supabase
        .from('route_stops')
        .select('id, stop_name, fare, stop_order')
        .eq('route_code', routeCode)
        .eq('is_active', true)
        .order('stop_order');

      setBus(busData);
      setStops(stopsData || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load route information');
    } finally {
      setIsLoading(false);
    }
  }, [routeCode]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDestinationSelect = (stop: RouteStop) => {
    router.push(`/admin/special-pass/buses/route-${routeCode}/destinations/${encodeURIComponent(stop.stop_name)}`);
  };

  if (isLoading) {
    return (
      <PageTransition>
        <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-orange-50 via-white to-red-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading destinations...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => router.back()}
                variant="outline" 
                size="sm"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
                  <MapPin className="w-8 h-8 text-orange-600" />
                  Select Destination
                </h1>
                <p className="text-gray-600">Choose your destination for {bus?.name}</p>
              </div>
            </div>
          </motion.div>

          {/* Booking Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <h3 className="font-semibold text-green-800">Booking Progress</h3>
                    <p className="text-sm text-green-700">
                      {specialPassData.studentName} ({specialPassData.admissionNumber}) | 
                      {bus?.name} ({routeCode}) | 
                      {new Date(specialPassData.goDate).toLocaleDateString()} - {new Date(specialPassData.returnDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Destinations */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
                <CardTitle className="text-2xl font-bold flex items-center gap-2">
                  <MapPin className="w-6 h-6" />
                  Available Destinations
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {stops.length === 0 ? (
                  <div className="text-center py-12">
                    <MapPin className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Destinations Available</h3>
                    <p className="text-gray-500">There are currently no active stops for this route.</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {stops.map((stop, index) => (
                      <motion.div
                        key={stop.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                      >
                        <Card 
                          className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-2 border-gray-200 hover:border-orange-300"
                          onClick={() => handleDestinationSelect(stop)}
                        >
                          <CardContent className="p-6">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="p-2 bg-orange-100 rounded-lg">
                                <MapPin className="w-6 h-6 text-orange-600" />
                              </div>
                              <div className="flex-1">
                                <h3 className="font-bold text-lg text-gray-800">{stop.stop_name}</h3>
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <Clock className="w-4 h-4" />
                                  <span>Stop #{stop.stop_order}</span>
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-2">
                                <DollarSign className="w-5 h-5 text-green-600" />
                                <span className="text-2xl font-bold text-green-700">₹{stop.fare}</span>
                              </div>
                            </div>

                            <Button 
                              className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDestinationSelect(stop);
                              }}
                            >
                              Select Destination
                            </Button>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassDestinations);
