'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { CheckCircle, ArrowLeft, User, Calendar, Bus, MapPin, DollarSign, CreditCard, AlertTriangle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface Bus {
  name: string;
  route_code: string;
}

interface RouteStop {
  stop_name: string;
  fare: number;
}

interface Params {
  routeId: string;
  destination: string;
}

function SpecialPassConfirmation({ params }: { params: Params }) {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, updateSpecialPassData } = useSpecialPass();
  const [bus, setBus] = useState<Bus | null>(null);
  const [stop, setStop] = useState<RouteStop | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const routeCode = decodeURIComponent(params.routeId.replace('route-', ''));
  const destinationName = decodeURIComponent(params.destination);

  // Comprehensive validation before allowing payment
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValidating, setIsValidating] = useState(true);

  // Comprehensive validation function
  const validateBookingData = async () => {
    const errors: string[] = [];

    // Check required data from previous steps
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      errors.push('Travel dates are missing');
    }

    if (!specialPassData.studentName || !specialPassData.admissionNumber) {
      errors.push('Student details are missing');
    }

    // Validate student name format (2-3 words)
    if (specialPassData.studentName) {
      const nameParts = specialPassData.studentName.trim().split(/\s+/);
      if (nameParts.length < 2 || nameParts.length > 3) {
        errors.push('Student name must contain 2-3 words (first name + surname + optional middle name)');
      }
    }

    // Validate admission number format
    if (specialPassData.admissionNumber) {
      const admissionRegex = /^[0-9]{2}[A-Z]{2,4}[0-9]{3}$/;
      if (!admissionRegex.test(specialPassData.admissionNumber)) {
        errors.push('Invalid admission number format. Expected: 2 digits + 2-4 letters + 3 digits');
      }
    }

    // Validate dates
    if (specialPassData.goDate && specialPassData.returnDate) {
      const goDateObj = new Date(specialPassData.goDate);
      const returnDateObj = new Date(specialPassData.returnDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (goDateObj < today) {
        errors.push('Go date cannot be in the past');
      }

      if (returnDateObj <= goDateObj) {
        errors.push('Return date must be after go date');
      }
    }

    // Check for duplicate booking
    if (specialPassData.admissionNumber && specialPassData.goDate && specialPassData.returnDate) {
      try {
        const { data: existingBooking } = await supabase
          .from('bookings')
          .select('id')
          .eq('admission_number', specialPassData.admissionNumber)
          .eq('go_date', specialPassData.goDate)
          .eq('return_date', specialPassData.returnDate)
          .eq('bus_route', routeCode)
          .single();

        if (existingBooking) {
          errors.push('A booking already exists for this student with the same dates and route');
        }
      } catch (error) {
        // Single query error is expected when no duplicate exists
      }
    }

    return errors;
  };

  // Check if we have required data from previous steps
  useEffect(() => {
    if (!specialPassData.goDate || !specialPassData.returnDate) {
      toast.error('Please select travel dates first');
      router.push('/admin/special-pass');
      return;
    }

    if (!specialPassData.studentName || !specialPassData.admissionNumber) {
      toast.error('Please enter student details first');
      router.push('/admin/special-pass/details');
      return;
    }
  }, [specialPassData, router]);

  const fetchData = useCallback(async () => {
    try {
      const { data: busData } = await supabase
        .from('buses')
        .select('name, route_code')
        .eq('route_code', routeCode)
        .eq('is_active', true)
        .single();

      const { data: stopData } = await supabase
        .from('route_stops')
        .select('stop_name, fare')
        .eq('route_code', routeCode)
        .eq('stop_name', destinationName)
        .eq('is_active', true)
        .single();

      if (!busData || !stopData) {
        toast.error('Invalid route or destination');
        router.push('/admin/special-pass/buses');
        return;
      }

      setBus(busData);
      setStop(stopData);

      // Update special pass context with bus and destination info
      updateSpecialPassData({
        busRoute: routeCode,
        busName: busData.name,
        destination: stopData.stop_name,
        fare: stopData.fare
      });

      // Perform comprehensive validation after data is loaded
      const errors = await validateBookingData();
      setValidationErrors(errors);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load booking details');
      router.push('/admin/special-pass/buses');
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [routeCode, destinationName]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleConfirmBooking = () => {
    // Check if validation is still in progress
    if (isValidating) {
      toast.error('Please wait while we validate your booking details');
      return;
    }

    // Check if there are any validation errors
    if (validationErrors.length > 0) {
      toast.error('Please resolve all validation errors before proceeding');
      return;
    }

    // All validations passed, proceed to payment
    router.push('/admin/special-pass/payment');
  };

  if (isLoading) {
    return (
      <PageTransition>
        <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-orange-50 via-white to-red-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-md"
          >
            <Card className="shadow-2xl border-0 bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading booking details...</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => router.back()}
                variant="outline" 
                size="sm"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
                  <CheckCircle className="w-8 h-8 text-orange-600" />
                  Confirm Special Booking
                </h1>
                <p className="text-gray-600">Review all details before proceeding to payment</p>
              </div>
            </div>
          </motion.div>

          {/* Validation Status */}
          {isValidating && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mb-8"
            >
              <Card className="border-blue-200 bg-blue-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
                    <div>
                      <h3 className="font-semibold text-blue-800">Validating Booking Details</h3>
                      <p className="text-sm text-blue-700">Please wait while we verify all booking information...</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Validation Errors */}
          {!isValidating && validationErrors.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mb-8"
            >
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
                    <div className="flex-1">
                      <h3 className="font-semibold text-red-800 mb-2">Validation Errors</h3>
                      <ul className="text-sm text-red-700 space-y-1">
                        {validationErrors.map((error, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-red-500 mt-1">•</span>
                            <span>{error}</span>
                          </li>
                        ))}
                      </ul>
                      <p className="text-sm text-red-600 mt-3 font-medium">
                        Please resolve these issues before proceeding to payment.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Booking Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
                <CardTitle className="text-2xl font-bold flex items-center gap-2">
                  <CheckCircle className="w-6 h-6" />
                  Special Bus Pass Booking Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="space-y-6">
                  {/* Student Information */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Student Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Name:</span>
                        <span className="ml-2 text-gray-900">{specialPassData.studentName}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Admission Number:</span>
                        <span className="ml-2 text-gray-900">{specialPassData.admissionNumber}</span>
                      </div>
                    </div>
                  </div>

                  {/* Travel Dates */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 className="font-semibold text-green-800 mb-3 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Travel Dates
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Go Date:</span>
                        <span className="ml-2 text-gray-900">
                          {new Date(specialPassData.goDate).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Return Date:</span>
                        <span className="ml-2 text-gray-900">
                          {new Date(specialPassData.returnDate).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                          })}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Bus and Route Information */}
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h3 className="font-semibold text-purple-800 mb-3 flex items-center gap-2">
                      <Bus className="w-5 h-5" />
                      Bus and Route Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Bus Name:</span>
                        <span className="ml-2 text-gray-900">{bus?.name}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Route Code:</span>
                        <span className="ml-2 text-gray-900">{routeCode}</span>
                      </div>
                    </div>
                  </div>

                  {/* Destination and Fare */}
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h3 className="font-semibold text-orange-800 mb-3 flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      Destination and Fare
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Destination:</span>
                        <span className="ml-2 text-gray-900">{stop?.stop_name}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Fare:</span>
                        <span className="ml-2 text-2xl font-bold text-green-700">₹{stop?.fare}</span>
                      </div>
                    </div>
                  </div>

                  {/* Payment Information */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
                      <CreditCard className="w-5 h-5" />
                      Payment Information
                    </h3>
                    <div className="text-sm">
                      <p className="text-gray-700">
                        <span className="font-medium">Total Amount:</span>
                        <span className="ml-2 text-2xl font-bold text-green-700">₹{stop?.fare}</span>
                      </p>
                      <p className="text-gray-600 mt-2">
                        You will have the option to pay online or mark as &quot;Pay at College&quot; on the next page.
                      </p>
                    </div>
                  </div>

                  {/* Confirmation Button */}
                  <div className="mt-8">
                    <Button
                      onClick={handleConfirmBooking}
                      disabled={isValidating || validationErrors.length > 0}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    >
                      {isValidating ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-5 h-5 animate-spin" />
                          Validating Booking Details...
                        </div>
                      ) : validationErrors.length > 0 ? (
                        'Resolve Validation Errors to Continue'
                      ) : (
                        'Confirm Booking & Proceed to Payment'
                      )}
                    </Button>
                  </div>

                  <p className="text-xs text-gray-500 text-center mt-4">
                    By confirming, you acknowledge that this is a special booking created through the admin panel.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassConfirmation);
