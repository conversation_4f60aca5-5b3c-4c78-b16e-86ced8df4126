'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { PageTransition } from '@/components/ui/page-transition';
import { useSpecialPass } from '@/contexts/SpecialPassContext';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { motion } from 'framer-motion';
import { CreditCard, ArrowLeft, DollarSign, Building, Smartphone, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

declare global {
  interface Window {
    Razorpay: any;
  }
}

function SpecialPassPayment() {
  const { user } = useAdmin();
  const router = useRouter();
  const { specialPassData, updateSpecialPassData } = useSpecialPass();
  const [isLoading, setIsLoading] = useState(false);
  const [processingMethod, setProcessingMethod] = useState<'online' | 'offline' | null>(null);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Simple check to ensure we reached this page through proper flow
  useEffect(() => {
    // Basic check - all comprehensive validation should have been done on confirmation page
    if (!specialPassData.busRoute || !specialPassData.destination || !specialPassData.fare) {
      toast.error('Invalid access. Please complete the booking process.');
      router.push('/admin/special-pass');
      return;
    }
  }, [specialPassData, router]);

  // Load Razorpay script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    script.onload = () => setRazorpayLoaded(true);
    script.onerror = () => {
      console.error('Failed to load Razorpay script');
      setRazorpayLoaded(false);
    };
    document.body.appendChild(script);

    return () => {
      const existingScript = document.querySelector('script[src="https://checkout.razorpay.com/v1/checkout.js"]');
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
    };
  }, []);

  const saveSpecialBooking = async (paymentStatus: boolean, razorpayData?: any) => {
    const bookingData = {
      studentName: specialPassData.studentName,
      admissionNumber: specialPassData.admissionNumber,
      busRoute: specialPassData.busRoute,
      destination: specialPassData.destination,
      paymentStatus,
      goDate: specialPassData.goDate,
      returnDate: specialPassData.returnDate,
      fare: specialPassData.fare,
      busName: specialPassData.busName,
      isSpecialBooking: true, // Flag to identify special bookings
      ...razorpayData,
    };

    const response = await fetch('/api/special-bookings', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(bookingData),
      credentials: 'include'
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
      throw new Error(result.error || 'Failed to save booking');
    }

    // Store the booking_id in the context for display on ticket
    if (result.data && result.data.booking_id) {
      updateSpecialPassData({
        booking_id: result.data.booking_id
      });
    }

    return result;
  };

  const handleOnlinePayment = async () => {
    if (!razorpayLoaded) {
      toast.error('Payment gateway is loading. Please try again.');
      return;
    }

    if (!specialPassData.fare || specialPassData.fare <= 0) {
      toast.error('Invalid fare amount. Please try again.');
      return;
    }

    setIsLoading(true);
    setProcessingMethod('online');
    setError(null);

    try {
      // Create order
      const orderResponse = await fetch('/api/payment/order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(specialPassData.fare * 100),
          currency: 'INR',
          receipt: `special_booking_${Date.now()}`,
        }),
      });

      const orderData = await orderResponse.json();

      if (!orderResponse.ok) {
        throw new Error(orderData.error || 'Failed to create order');
      }

      const options = {
        key: orderData.keyId,
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: 'St. Joseph\'s College Bus Pass',
        description: `Special Bus Pass - ${specialPassData.destination}`,
        order_id: orderData.order.id,
        handler: async (response: any) => {
          try {
            // Verify payment
            const verifyResponse = await fetch('/api/payment/verify', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                razorpay_order_id: response.razorpay_order_id,
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_signature: response.razorpay_signature,
              }),
            });

            const verifyData = await verifyResponse.json();

            if (verifyResponse.ok && verifyData.success) {
              await saveSpecialBooking(true, {
                razorpay_payment_id: response.razorpay_payment_id,
                razorpay_order_id: response.razorpay_order_id,
                razorpay_signature: response.razorpay_signature,
              });

              updateSpecialPassData({
                paymentStatus: true,
                paymentMethod: 'online'
              });

              toast.success('Payment successful! Special booking confirmed.');
              router.push('/admin/special-pass/ticket');
            } else {
              throw new Error(verifyData.error || 'Payment verification failed');
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Payment verification failed';
            setError(errorMessage);
            toast.error(errorMessage);
          } finally {
            setIsLoading(false);
            setProcessingMethod(null);
          }
        },
        prefill: {
          name: specialPassData.studentName,
          email: user?.email || '',
        },
        theme: {
          color: '#ea580c',
        },
        modal: {
          ondismiss: () => {
            setIsLoading(false);
            setProcessingMethod(null);
            toast.info('Payment cancelled');
          },
        },
      };

      const razorpay = new window.Razorpay(options);
      razorpay.open();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Payment failed. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
      setIsLoading(false);
      setProcessingMethod(null);
    }
  };

  const handleOfflinePayment = async () => {
    setIsLoading(true);
    setProcessingMethod('offline');
    setError(null);

    try {
      await saveSpecialBooking(false, {
        razorpay_payment_id: null,
        razorpay_order_id: null,
        razorpay_signature: null,
      });

      updateSpecialPassData({
        paymentStatus: false,
        paymentMethod: 'offline'
      });

      toast.success('Special booking confirmed! Payment to be collected at college.');
      router.push('/admin/special-pass/ticket');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Booking failed. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
      setProcessingMethod(null);
    }
  };

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gradient-to-br from-orange-50 via-white to-red-50">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => router.back()}
                variant="outline" 
                size="sm"
                disabled={isLoading}
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
                  <CreditCard className="w-8 h-8 text-orange-600" />
                  Payment Options
                </h1>
                <p className="text-gray-600">Choose your preferred payment method for the special booking</p>
              </div>
            </div>
          </motion.div>

          {/* Booking Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card className="border-green-200 bg-green-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-green-800">Special Booking Summary</h3>
                    <p className="text-sm text-green-700">
                      {specialPassData.studentName} | {specialPassData.busName} | {specialPassData.destination}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-green-700">Total Amount</p>
                    <p className="text-2xl font-bold text-green-800">₹{specialPassData.fare}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Error Display */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                    <div>
                      <h3 className="font-semibold text-red-800">Payment Error</h3>
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Payment Options */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="shadow-lg border-orange-200">
              <CardHeader className="bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-t-lg">
                <CardTitle className="text-2xl font-bold flex items-center gap-2">
                  <CreditCard className="w-6 h-6" />
                  Select Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Online Payment */}
                  <Card className="border-2 border-blue-200 hover:border-blue-400 transition-colors">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <div className="p-4 bg-blue-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                          <Smartphone className="w-8 h-8 text-blue-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">Online Payment</h3>
                        <p className="text-gray-600 mb-4">Pay securely using Razorpay</p>
                        <p className="text-2xl font-bold text-blue-600 mb-4">₹{specialPassData.fare}</p>
                        <Button
                          onClick={handleOnlinePayment}
                          disabled={isLoading || !razorpayLoaded}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          {isLoading && processingMethod === 'online' ? (
                            <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              Processing...
                            </div>
                          ) : (
                            'Pay Online Now'
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Offline Payment */}
                  <Card className="border-2 border-green-200 hover:border-green-400 transition-colors">
                    <CardContent className="p-6">
                      <div className="text-center">
                        <div className="p-4 bg-green-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                          <Building className="w-8 h-8 text-green-600" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-800 mb-2">Pay at College</h3>
                        <p className="text-gray-600 mb-4">Student will pay at college office</p>
                        <p className="text-2xl font-bold text-green-600 mb-4">₹{specialPassData.fare}</p>
                        <Button
                          onClick={handleOfflinePayment}
                          disabled={isLoading}
                          className="w-full bg-green-600 hover:bg-green-700"
                        >
                          {isLoading && processingMethod === 'offline' ? (
                            <div className="flex items-center gap-2">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              Processing...
                            </div>
                          ) : (
                            'Mark as Pay at College'
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-sm text-gray-500">
                    Both payment methods will generate a valid bus pass ticket for the student.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(SpecialPassPayment);
