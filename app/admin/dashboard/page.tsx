'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PageTransition } from '@/components/ui/page-transition';
import { useAdmin, withAdminAuth } from '@/contexts/AdminContext';
import { Settings, Bus, Calendar, Users, LogOut, Route, BookOpen, ChevronLeft, ChevronRight, RotateCcw, BarChart3, Database, CalendarCheck, Ticket, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Calendar as CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar2 } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface AdminData {
  bookingEnabled: boolean;
  goDate: string;
  returnDate: string;
  busAvailability: { [key: string]: number };
}

interface Bus {
  id: number;
  name: string;
  route_code: string;
  is_active: boolean;
  total_seats: number;
  route_is_active?: boolean; // Add route active status
}



interface NewBookingStats {
  totalBuses: number;
  totalBookings: number;
  currentBookings: number;
  currentRevenue: number;
  availableSeats: number;
  totalCapacity: number;
  occupancyRate: string;
}

interface Booking {
  id: number;
  admission_number: string;
  student_name: string;
  bus_route: string;
  destination: string;
  payment_status: boolean;
  created_at: string;
  boarded: boolean;
}

function AdminDashboard() {
  const { user, logout } = useAdmin();
  const router = useRouter();
  const [adminData, setAdminData] = useState<AdminData>({
    bookingEnabled: false,
    goDate: '',
    returnDate: '',
    busAvailability: {},
  });

  const [newBookingStats, setNewBookingStats] = useState<NewBookingStats>({
    totalBuses: 0,
    totalBookings: 0,
    currentBookings: 0,
    currentRevenue: 0,
    availableSeats: 0,
    totalCapacity: 0,
    occupancyRate: '0.0',
  });
  const [buses, setBuses] = useState<Bus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdatingBookingControl, setIsUpdatingBookingControl] = useState(false);

  // New: track explicit logout
  // const [isLoggedOut, setIsLoggedOut] = useState(false);

  const fetchAllData = useCallback(async () => {
    try {
      await Promise.all([
        fetchAdminData(),
        fetchBuses(),
        fetchNewBookingStats()
      ]);
    } catch (error) {
      toast.error('Failed to fetch data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // New: warn user when closing/navigating away if they haven't logged out
  // useEffect(() => {
  //   const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  //     if (!isLoggedOut) {
  //       const message = 'You have not logged out!!';
  //       e.preventDefault();
  //       // Some browsers use returnValue; setting it ensures the prompt appears.
  //       e.returnValue = message;
  //       return message;
  //     }
  //     return undefined;
  //   };

  //   window.addEventListener('beforeunload', handleBeforeUnload);
  //   return () => {
  //     window.removeEventListener('beforeunload', handleBeforeUnload);
  //   };
  // }, [isLoggedOut]);

  // Helper: validate DDMMYYYY string
  const isValidDDMMYYYY = (s: string) => {
    if (!/^\d{8}$/.test(s)) return false;
    const day = Number(s.slice(0, 2));
    const month = Number(s.slice(2, 4));
    const year = Number(s.slice(4, 8));
    const dt = new Date(year, month - 1, day);
    return (
      dt.getFullYear() === year &&
      dt.getMonth() === month - 1 &&
      dt.getDate() === day
    );
  };

  // Helper: format Date -> DDMMYYYY
  const formatDateToDDMMYYYY = (d: Date) => {
    const dd = String(d.getDate()).padStart(2, '0');
    const mm = String(d.getMonth() + 1).padStart(2, '0');
    const yyyy = String(d.getFullYear());
    return `${dd}${mm}${yyyy}`;
  };

  // Helper: parse DDMMYYYY -> Date
  const parseDDMMYYYY = (s: string) => {
    if (!/^\d{8}$/.test(s)) return undefined;
    const day = Number(s.slice(0, 2));
    const month = Number(s.slice(2, 4)) - 1; // JS months are 0-indexed
    const year = Number(s.slice(4, 8));
    return new Date(year, month, day);
  };

  // New helper function: convert DDMMYYYY to ISO format (YYYY-MM-DD)
  const formatToISODate = (ddmmyyyy: string): string => {
    if (!/^\d{8}$/.test(ddmmyyyy)) return '';
    const day = ddmmyyyy.slice(0, 2);
    const month = ddmmyyyy.slice(2, 4);
    const year = ddmmyyyy.slice(4, 8);
    return `${year}-${month}-${day}`;
  };

  const fetchAdminData = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          const data = result.data;
          // If backend returns YYYY-MM-DD, convert to DDMMYYYY for inputs
          const normalizeDate = (dt?: string) => {
            if (!dt) return '';
            // yyyy-mm-dd -> ddmmyyyy
            if (/^\d{4}-\d{2}-\d{2}$/.test(dt)) {
              const [y, m, d] = dt.split('-');
              return `${d}${m}${y}`;
            }
            // already ddmmyyyy -> return as is
            if (/^\d{8}$/.test(dt)) return dt;
            // fallback: try to parse ISO
            const parsed = new Date(dt);
            if (!isNaN(parsed.getTime())) return formatDateToDDMMYYYY(parsed);
            return '';
          };

          setAdminData(prev => ({
            ...prev,
            ...data,
            goDate: normalizeDate(data.goDate),
            returnDate: normalizeDate(data.returnDate),
            busAvailability: data.busAvailability || prev.busAvailability
          }));
        }
      }
    } catch (error) {
      console.error('Failed to fetch admin data:', error);
    }
  };

  const fetchBuses = async () => {
    try {
      const response = await fetch('/api/admin/buses');
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setBuses(result.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch buses:', error);
    }
  };



  const fetchNewBookingStats = async () => {
    try {
      // Use the new detailed statistics endpoint for comprehensive data
      const response = await fetch('/api/admin/analytics/detailed-stats');
      const result = await response.json();

      if (result.success && result.data) {
        setNewBookingStats(result.data);
      } else {
        // Fallback to individual endpoints if detailed stats fail
        const [totalBusesRes, totalBookingsRes, currentBookingsRes, availableSeatsRes] = await Promise.all([
          fetch('/api/admin/analytics/total-buses'),
          fetch('/api/admin/analytics/total-bookings'),
          fetch('/api/admin/analytics/current-bookings'),
          fetch('/api/admin/analytics/available-seats')
        ]);

        const [totalBusesData, totalBookingsData, currentBookingsData, availableSeatsData] = await Promise.all([
          totalBusesRes.json(),
          totalBookingsRes.json(),
          currentBookingsRes.json(),
          availableSeatsRes.json()
        ]);

        // Update state with individual endpoint results
        setNewBookingStats({
          totalBuses: totalBusesData.success ? totalBusesData.data : 0,
          totalBookings: totalBookingsData.success ? totalBookingsData.data : 0,
          currentBookings: currentBookingsData.success ? currentBookingsData.data : 0,
          currentRevenue: 0, // Not available from individual endpoints
          availableSeats: availableSeatsData.success ? availableSeatsData.data : 0,
          totalCapacity: 0, // Not available from individual endpoints
          occupancyRate: '0.0'
        });
      }
    } catch (error) {
      console.error('Failed to fetch new booking stats:', error);
      // Set default values on error
      setNewBookingStats({
        totalBuses: 0,
        totalBookings: 0,
        currentBookings: 0,
        currentRevenue: 0,
        availableSeats: 0,
        totalCapacity: 0,
        occupancyRate: '0.0'
      });
    }
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);

    try {
      // Validate DDMMYYYY format
      const go = adminData.goDate;
      const ret = adminData.returnDate;

      if (!isValidDDMMYYYY(go)) {
        toast.error('Go Date must be in DDMMYYYY format and valid');
        setIsSaving(false);
        return;
      }
      if (!isValidDDMMYYYY(ret)) {
        toast.error('Return Date must be in DDMMYYYY format and valid');
        setIsSaving(false);
        return;
      }

      const goDateObj = parseDDMMYYYY(go);
      const returnDateObj = parseDDMMYYYY(ret);

      // Ensure returnDate is after goDate
      if (!goDateObj || !returnDateObj || returnDateObj <= goDateObj) {
        toast.error('Return date must be after go date');
        setIsSaving(false);
        return;
      }

      // Convert DDMMYYYY format to PostgreSQL-compatible YYYY-MM-DD
      const payload = { 
        ...adminData,
        // Convert date format for the API
        goDate: formatToISODate(adminData.goDate),
        returnDate: formatToISODate(adminData.returnDate)
      };

      const response = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Dates updated successfully');
      } else {
        let errorMessage = result.error || 'Failed to update dates';
        // Provide more specific error message for date format issues
        if (errorMessage.includes('date/time field')) {
          errorMessage = 'Invalid date format. Please ensure dates are properly formatted.';
        }
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Save settings error:', error);
      toast.error('Failed to update dates. Please check date format.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleResetSeats = async () => {
    if (!confirm('Are you sure you want to reset all booking data? This will delete all current bookings and restore all seats to their maximum capacity.')) {
      return;
    }

    setIsResetting(true);

    try {
      const response = await fetch('/api/admin/analytics/reset', {
        method: 'POST',
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Current bookings reset successfully');
        // Refresh data to show updated statistics
        await fetchAllData();
      } else {
        toast.error(result.error || 'Failed to reset current bookings');
      }
    } catch (error) {
      toast.error('Failed to reset current bookings');
    } finally {
      setIsResetting(false);
    }
  };

  const handleBookingToggle = async (checked: boolean) => {
    // Prevent multiple clicks by setting loading state
    setIsUpdatingBookingControl(true);
    
    const newData = { ...adminData, bookingEnabled: checked };

    // We'll only set default dates if the current dates are empty
    if (checked && (!newData.goDate || !newData.returnDate)) {
      const today = new Date();
      const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      newData.goDate = formatDateToDDMMYYYY(today);
      newData.returnDate = formatDateToDDMMYYYY(nextWeek);
    }

    // Note: We don't update adminData state immediately to maintain toggle visual state during loading
    // This creates a better UX as the toggle doesn't visually change until the operation completes

    try {
      // Convert DDMMYYYY to YYYY-MM-DD for API
      const payload = {
        ...newData,
        goDate: formatToISODate(newData.goDate),
        returnDate: formatToISODate(newData.returnDate)
      };

      const response = await fetch('/api/admin/settings', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'include',
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Update state after successful API call
        setAdminData(newData);
        toast.success(`Booking ${checked ? 'enabled' : 'disabled'} successfully`);
        if (checked) setIsModalOpen(true); // Open modal when booking is enabled
      } else {
        // Don't update adminData on error
        let errorMessage = result.error || 'Failed to update booking status';
        if (errorMessage.includes('date/time field')) {
          errorMessage = 'Invalid date format. Please ensure dates are properly formatted.';
        }
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('Booking toggle error:', error);
      toast.error('Failed to update booking status');
    } finally {
      // Always clear loading state whether successful or not
      setIsUpdatingBookingControl(false);
    }
  };

  // Additional state for dates in Date object format
  const [goDateObj, setGoDateObj] = useState<Date | undefined>(undefined);
  const [returnDateObj, setReturnDateObj] = useState<Date | undefined>(undefined);

  // Update date objects when adminData changes
  useEffect(() => {
    setGoDateObj(parseDDMMYYYY(adminData.goDate));
    setReturnDateObj(parseDDMMYYYY(adminData.returnDate));
  }, [adminData.goDate, adminData.returnDate]);

  // Handle go date selection
  const handleGoDateSelect = (date: Date | undefined) => {
    if (!date) return;
    
    setGoDateObj(date);
    const formattedDate = formatDateToDDMMYYYY(date);
    setAdminData(prev => ({ ...prev, goDate: formattedDate }));
    
    // If return date is before go date, update return date
    if (returnDateObj && date > returnDateObj) {
      // Set return date to day after go date
      const newReturnDate = new Date(date);
      newReturnDate.setDate(newReturnDate.getDate() + 1);
      setReturnDateObj(newReturnDate);
      setAdminData(prev => ({ 
        ...prev, 
        returnDate: formatDateToDDMMYYYY(newReturnDate)
      }));
      toast.info("Return date adjusted to be after go date");
    }
  };

  // Handle return date selection
  const handleReturnDateSelect = (date: Date | undefined) => {
    if (!date) return;
    
    // If selected return date is before go date, show error
    if (goDateObj && date < goDateObj) {
      toast.error("Return date must be after go date");
      return;
    }
    
    setReturnDateObj(date);
    const formattedDate = formatDateToDDMMYYYY(date);
    setAdminData(prev => ({ ...prev, returnDate: formattedDate }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Admin Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user?.full_name}</p>
            </div>
            <Button
              variant="outline"
              className="text-red-600 border-red-600 hover:bg-red-50"
              onClick={() => logout()}
            >
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </motion.div>

          {/* Navigation Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.05 }}
            className="mb-8"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  Quick Navigation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Bus & Route Management Group */}
                  <Button
                    onClick={() => router.push('/admin/buses')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    <Bus className="w-5 h-5" />
                    Bus Management
                  </Button>
                  <Button
                    onClick={() => router.push('/admin/routes')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-indigo-300 text-indigo-700 hover:bg-indigo-50"
                  >
                    <Route className="w-5 h-5" />
                    Route Management
                  </Button>
                  
                  {/* Student Management */}
                  <Button
                    onClick={() => router.push('/admin/students')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-green-300 text-green-700 hover:bg-green-50"
                  >
                    <Users className="w-5 h-5" />
                    Student Management
                  </Button>

                  {/* Staff Management */}
                  <Button
                    onClick={() => router.push('/admin/staff')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-teal-300 text-teal-700 hover:bg-teal-50"
                  >
                    <Users className="w-5 h-5" />
                    Staff Management
                  </Button>
                  
                  {/* Special Pass */}
                  <Button
                    onClick={() => router.push('/admin/special-pass')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-orange-300 text-orange-700 hover:bg-orange-50"
                  >
                    <Ticket className="w-5 h-5" />
                    Special Pass
                  </Button>
                  
                  {/* Booking Management Group */}
                  <Button
                    onClick={() => router.push('/admin/current-bookings')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-purple-300 text-purple-700 hover:bg-purple-50"
                  >
                    <CalendarCheck className="w-5 h-5" />
                    Current Bookings
                  </Button>
                  <Button
                    onClick={() => router.push('/admin/all-bookings')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-violet-300 text-violet-700 hover:bg-violet-50"
                  >
                    <Database className="w-5 h-5" />
                    All Bookings
                  </Button>
                  
                  {/* Reports Group */}
                  <Button
                    onClick={() => router.push('/admin/reports')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-red-300 text-red-700 hover:bg-red-50"
                  >
                    <BarChart3 className="w-5 h-5" />
                    Reports Dashboard
                  </Button>
                  <Button
                    onClick={() => router.push('/admin/custom-reports')}
                    variant="outline"
                    className="flex items-center gap-2 h-12 border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    <FileText className="w-5 h-5" />
                    Custom Report
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Booking Control */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.15 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Booking Control
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="booking-toggle" className={isUpdatingBookingControl ? "text-gray-400" : ""}>
                      {isUpdatingBookingControl 
                        ? adminData.bookingEnabled 
                          ? "Disabling Booking..." 
                          : "Enabling Booking..."
                        : "Enable Booking"}
                    </Label>
                    <div className="flex items-center">
                      {isUpdatingBookingControl && (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-3"></div>
                      )}
                      <Switch
                        id="booking-toggle"
                        checked={adminData.bookingEnabled}
                        onCheckedChange={handleBookingToggle}
                        disabled={isUpdatingBookingControl}
                        className={isUpdatingBookingControl ? "opacity-70" : ""}
                      />
                    </div>
                  </div>
                  <Badge
                    variant={adminData.bookingEnabled ? "default" : "secondary"}
                    className={`mt-2 ${adminData.bookingEnabled ? 'bg-green-600' : 'bg-red-600'} ${
                      isUpdatingBookingControl ? 'opacity-70' : ''
                    }`}
                  >
                    {isUpdatingBookingControl 
                      ? (adminData.bookingEnabled ? "Disabling..." : "Enabling...") 
                      : (adminData.bookingEnabled ? 'Active' : 'Inactive')}
                  </Badge>
                </CardContent>
              </Card>
            </motion.div>

            {/* Travel Dates */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Travel Dates
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="go-date">Go Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !goDateObj && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {goDateObj ? format(goDateObj, "dd MMM yyyy") : <span>Select date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar2
                          mode="single"
                          selected={goDateObj}
                          onSelect={handleGoDateSelect}
                          disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label htmlFor="return-date">Return Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !returnDateObj && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {returnDateObj ? format(returnDateObj, "dd MMM yyyy") : <span>Select date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar2
                          mode="single"
                          selected={returnDateObj}
                          onSelect={handleReturnDateSelect}
                          disabled={(date) => 
                            date < new Date(new Date().setHours(0, 0, 0, 0)) || 
                            (goDateObj ? date <= goDateObj : false)
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <Button
                    onClick={handleSaveSettings}
                    disabled={isSaving || !goDateObj || !returnDateObj}
                    size="lg"
                    className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-semibold px-8 py-3 rounded-lg transition-all duration-300 transform hover:scale-105 mx-auto block"
                  >
                    {isSaving ? 'Saving...' : 'Update Dates'}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* New Statistics Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-8"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Booking Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  {/* Bus Fleet Statistics */}
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{newBookingStats.totalBuses}</div>
                    <div className="text-sm text-gray-600">Total Buses</div>
                  </div>
                  
                  {/* Booking Statistics */}
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{newBookingStats.totalBookings}</div>
                    <div className="text-sm text-gray-600">Total Bookings</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{newBookingStats.currentBookings}</div>
                    <div className="text-sm text-gray-600">Current Bookings</div>
                  </div>
                </div>

                {/* Financial & Occupancy Statistics */}
                <div className="grid grid-cols-2 gap-4 mb-6 pt-4 border-t border-gray-200">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-xl font-bold text-green-600">₹{newBookingStats.currentRevenue}</div>
                    <div className="text-sm text-gray-600">Current Revenue</div>
                  </div>
                  <div className="text-center p-3 bg-indigo-50 rounded-lg">
                    <div className="text-xl font-bold text-indigo-600">{newBookingStats.occupancyRate}%</div>
                    <div className="text-sm text-gray-600">Occupancy Rate</div>
                  </div>
                </div>

                {/* Capacity Information */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Total Capacity:</span>
                    <span className="font-semibold text-gray-800">{newBookingStats.totalCapacity} seats</span>
                  </div>
                  <div className="flex justify-between items-center text-sm mt-2">
                    <span className="text-gray-600">Occupied Seats:</span>
                    <span className="font-semibold text-gray-800">{newBookingStats.totalCapacity - newBookingStats.availableSeats} seats</span>
                  </div>
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${newBookingStats.occupancyRate}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                {/* Reset Seats Button */}
                <div className="text-center">
                  <Button
                    onClick={handleResetSeats}
                    disabled={isResetting}
                    variant="outline"
                    className="border-red-600 text-red-600 hover:bg-red-50"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    {isResetting ? 'Resetting...' : 'Reset All Data'}
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    This will delete all current bookings and restore all seats to maximum capacity
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Bus Availability Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white">
                <CardTitle className="flex items-center gap-3 text-xl">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Bus className="w-6 h-6" />
                  </div>
                  Bus Fleet Status
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 bg-gradient-to-br from-gray-50 to-blue-50/30">
                {buses.length === 0 ? (
                  <div className="text-center py-12">
                    <Bus className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500 text-lg">No buses available</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {buses.map((bus, index) => {
                      const availableSeats = adminData.busAvailability[bus.route_code] || 0;
                      const occupancyRate = Math.round(((bus.total_seats - availableSeats) / bus.total_seats) * 100);
                      const isRouteInactive = bus.route_is_active === false || !bus.is_active;
                      const isFullyBooked = !isRouteInactive && availableSeats === 0;
                      const isAlmostFull = !isRouteInactive && occupancyRate > 80;
                      
                      return (
                        <motion.div
                          key={bus.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="group"
                        >
                          <div className={`relative p-5 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                            isRouteInactive
                              ? 'bg-gray-50 border-gray-300 hover:border-gray-400'
                              : isFullyBooked 
                                ? 'bg-red-50 border-red-200 hover:border-red-300' 
                                : isAlmostFull 
                                  ? 'bg-yellow-50 border-yellow-200 hover:border-yellow-300' 
                                  : 'bg-green-50 border-green-200 hover:border-green-300'
                          }`}>
                            {/* Status Indicator */}
                            <div className={`absolute top-3 right-3 w-3 h-3 rounded-full ${
                              isRouteInactive
                                ? 'bg-gray-400'
                                : isFullyBooked 
                                  ? 'bg-red-500' 
                                  : isAlmostFull 
                                    ? 'bg-yellow-500' 
                                    : 'bg-green-500'
                            } ${isRouteInactive ? '' : 'animate-pulse'}`}></div>

                            {/* Bus Name */}
                            <div className="mb-4">
                              <h4 className={`text-lg font-bold transition-colors ${
                                isRouteInactive 
                                  ? 'text-gray-500 group-hover:text-gray-600' 
                                  : 'text-gray-800 group-hover:text-gray-900'
                              }`}>
                                {bus.name}
                              </h4>
                              <p className={`text-sm ${
                                isRouteInactive ? 'text-gray-400' : 'text-gray-500'
                              }`}>Route: {bus.route_code}</p>
                            </div>

                            {/* Seat Information */}
                            <div className="space-y-3">
                              <div className="flex justify-between items-center">
                                <span className={`text-sm font-medium ${
                                  isRouteInactive ? 'text-gray-400' : 'text-gray-600'
                                }`}>
                                  {isRouteInactive ? 'Status' : 'Available Seats'}
                                </span>
                                <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                                  isRouteInactive
                                    ? 'bg-gray-100 text-gray-600'
                                    : isFullyBooked 
                                      ? 'bg-red-100 text-red-700' 
                                      : isAlmostFull 
                                        ? 'bg-yellow-100 text-yellow-700' 
                                        : 'bg-green-100 text-green-700'
                                }`}>
                                  {isRouteInactive ? 'INACTIVE' : `${availableSeats} / ${bus.total_seats}`}
                                </div>
                              </div>

                              {/* Progress Bar - only show for active routes */}
                              {!isRouteInactive && (
                                <div className="w-full">
                                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>Occupancy</span>
                                    <span>{occupancyRate}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
                                    <div
                                      className={`h-2.5 rounded-full transition-all duration-500 ${
                                        isFullyBooked 
                                          ? 'bg-gradient-to-r from-red-400 to-red-600' 
                                          : isAlmostFull 
                                            ? 'bg-gradient-to-r from-yellow-400 to-orange-500' 
                                            : 'bg-gradient-to-r from-green-400 to-blue-500'
                                      }`}
                                      style={{ width: `${occupancyRate}%` }}
                                    ></div>
                                  </div>
                                </div>
                              )}

                              {/* Inactive route message */}
                              {isRouteInactive && (
                                <div className="w-full text-center py-2">
                                  <div className="text-xs text-gray-400">
                                    Route is currently inactive
                                  </div>
                                </div>
                              )}

                              {/* Status Badge */}
                              <div className="flex justify-center pt-2">
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs font-medium border-2 ${
                                    isRouteInactive
                                      ? 'border-gray-300 text-gray-600 bg-gray-50'
                                      : isFullyBooked 
                                        ? 'border-red-300 text-red-700 bg-red-50' 
                                        : isAlmostFull 
                                          ? 'border-yellow-300 text-yellow-700 bg-yellow-50' 
                                          : 'border-green-300 text-green-700 bg-green-50'
                                  }`}
                                >
                                  {isRouteInactive
                                    ? 'ROUTE INACTIVE'
                                    : isFullyBooked 
                                      ? 'FULLY BOOKED' 
                                      : isAlmostFull 
                                        ? 'ALMOST FULL' 
                                        : 'AVAILABLE'
                                  }
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Hardcoded Modal */}
          {isModalOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
              <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
                <h2 className="text-xl font-bold text-gray-800 mb-4">Booking Enabled</h2>
                <p className="text-gray-600">Booking has been successfully enabled with the following travel dates:</p>
                <ul className="list-disc list-inside mt-2 text-gray-600">
                  <li>Go Date: {goDateObj ? format(goDateObj, "dd MMM yyyy") : adminData.goDate}</li>
                  <li>Return Date: {returnDateObj ? format(returnDateObj, "dd MMM yyyy") : adminData.returnDate}</li>
                </ul>
                <div className="mt-4 text-right">
                  <Button
                    onClick={() => setIsModalOpen(false)}
                    className="bg-blue-600 text-white"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(AdminDashboard);